import WebSocket from 'ws';
import { spawn } from 'child_process';
import axios from 'axios';
import path from 'path';
import { TestQueueService } from './services/test-queue.service';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

interface TestExecutionRequest {
  type: string;
  token: string;
  authToken?: string;
  testCaseId: string;
  tcId: string;
  projectId: string;
  testRunId: string;
  steps: any[];
  testCase: {
    title: string;
    precondition: string;
    expectation: string;
    projectId: string;
  };
}



class TestRunnerWebSocketServer {
  private wss: WebSocket.Server;
  private port: number;
  private backendUrl: string;
  private queueService: TestQueueService;
  private connectionCounter: number = 0;

  constructor(port: number = 3009) {
    this.port = port;
    this.backendUrl = process.env.BACKEND_URL || 'http://localhost:3010';

    // Initialize queue service
    this.queueService = new TestQueueService();

    this.wss = new WebSocket.Server({
      port: this.port,
      perMessageDeflate: false
    });

    console.log(`🚀 TestRun WebSocket server with BullMQ running on port ${this.port}`);
    this.setupWebSocketHandlers();
  }

  private setupWebSocketHandlers() {
    this.wss.on('connection', (ws: WebSocket) => {
      // Assign unique ID to this connection
      const wsId = `ws-${++this.connectionCounter}-${Date.now()}`;
      console.log(`📡 Client connected to TestRun server: ${wsId}`);

      // Add connection to queue service
      this.queueService.addConnection(wsId, ws);

      let isAuthenticated = false;

      ws.on('message', async (message: string) => {
        try {
          const data = JSON.parse(message);
          console.log('Received message type:', data.type);

          switch (data.type) {
            case 'authenticate':
              console.log('Validating JWT token:', data.authToken?.substring(0, 8) + '...');

              try {
                // Use the JWT token (authToken) for authentication, not the AgentQ API key
                const authToken = data.authToken || data.token;

                if (!authToken) {
                  throw new Error('No authentication token provided');
                }

                // Validate JWT token with backend by trying to access a protected endpoint
                const response = await axios.get(`${this.backendUrl}/api-keys`, {
                  headers: {
                    'Authorization': `Bearer ${authToken}`
                  }
                });

                console.log('JWT validation response status:', response.status);

                if (response.status === 200) {
                  isAuthenticated = true;
                  ws.send(JSON.stringify({
                    type: 'auth_success',
                    message: 'Authentication successful'
                  }));
                  console.log('Authentication successful for JWT token:', authToken?.substring(0, 8) + '..., waiting for commands...');
                } else {
                  throw new Error('Invalid JWT token');
                }
              } catch (error: any) {
                console.log('Authentication failed:', error.response?.data?.message || error.message);
                ws.send(JSON.stringify({
                  type: 'auth_failed',
                  message: error.response?.data?.message || error.message || 'Authentication failed'
                }));
              }
              break;

            case 'execute_test':
              if (!isAuthenticated) {
                ws.send(JSON.stringify({
                  type: 'test_error',
                  message: 'Not authenticated'
                }));
                return;
              }

              console.log(`📋 Queueing test: ${data.testCase?.title} for connection ${wsId}`);
              try {
                // Add test to queue instead of executing directly
                const job = await this.queueService.addTestJob(data, data.authToken, wsId, this.backendUrl);

                // Send queue status to client
                const queueStats = await this.queueService.getQueueStats();
                ws.send(JSON.stringify({
                  type: 'queue_status',
                  jobId: job.id,
                  position: queueStats.waiting,
                  totalInQueue: queueStats.total,
                  message: `Test queued. Position in queue: ${queueStats.waiting + 1}`
                }));
              } catch (queueError) {
                console.error('Failed to queue test:', queueError);
                ws.send(JSON.stringify({
                  type: 'test_error',
                  message: 'Failed to queue test for execution'
                }));
              }
              break;

            default:
              console.log('Unknown message type:', data.type);
          }
        } catch (error) {
          console.error('Error processing message:', error);
          ws.send(JSON.stringify({
            type: 'error',
            message: 'Invalid message format'
          }));
        }
      });

      ws.on('close', () => {
        console.log(`📡 Client disconnected from TestRun server: ${wsId}`);
        this.queueService.removeConnection(wsId);
      });

      ws.on('error', (error) => {
        console.error(`❌ WebSocket error for ${wsId}:`, error);
        this.queueService.removeConnection(wsId);
      });
    });
  }

  private async executeTest(ws: WebSocket, testData: TestExecutionRequest) {
    const { testCaseId, tcId, projectId, testRunId, steps, testCase, authToken } = testData;

    try {
      // Fetch AgentQ API key from backend using JWT token
      let agentqApiKey = '';
      try {
        const apiKeysResponse = await axios.get(`${this.backendUrl}/api-keys`, {
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        });

        if (apiKeysResponse.data && apiKeysResponse.data.length > 0) {
          agentqApiKey = apiKeysResponse.data[0].apiKey;
          console.log('Retrieved AgentQ API key for test execution');
        }
      } catch (error) {
        console.warn('Failed to fetch AgentQ API key, test may fail:', error);
      }

      // Create test data for master.spec.ts format
      const testDataContent = {
        testCase: {
          id: testCaseId,
          tcId: tcId.toString(),
          title: testCase.title,
          precondition: testCase.precondition,
          expectation: testCase.expectation
        },
        projectId,
        testRunId,
        steps: steps || [],
        authToken: authToken || null
      };

      // Use testrun-detail.spec.ts which has backend integration for TestRunDetail
      const testFilePath = path.join(__dirname, '../tests/testrun-detail.spec.ts');
      console.log('Executing: npx playwright test "' + testFilePath + '" --reporter=line');

      // Execute the test
      const testProcess = spawn('npx', [
        'playwright',
        'test',
        testFilePath,
        '--reporter=line'
      ], {
        cwd: path.join(__dirname, '..'), // Run from the root directory
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          TEST_DATA: JSON.stringify(testDataContent), // Pass test data as JSON string
          BACKEND_URL: this.backendUrl,
          PROJECT_ID: projectId,
          TEST_RUN_ID: testRunId,
          TEST_CASE_ID: testCaseId,
          AUTH_TOKEN: authToken || '',
          AGENTQ_TOKEN: agentqApiKey, // AgentQ library looks for this variable
          AGENTQ_API_KEY: agentqApiKey, // Alternative variable name
          AGENTQ_JWT_TOKEN: authToken // Pass JWT token for AgentQ library authentication
        }
      });

      ws.send(JSON.stringify({
        type: 'test_start',
        message: 'Test execution started'
      }));

      console.log('Connection stabilized');

      // Handle test output
      testProcess.stdout.on('data', (data) => {
        const output = data.toString();
        console.log('Test output:', output);
        ws.send(JSON.stringify({
          type: 'test_output',
          output: output
        }));
      });

      testProcess.stderr.on('data', (data) => {
        const error = data.toString();
        console.log('Test error:', error);
        ws.send(JSON.stringify({
          type: 'test_output',
          output: error
        }));
      });

      // Handle test completion
      testProcess.on('close', async (code) => {
        console.log('Test process exited with code:', code);

        const status = code === 0 ? 'passed' : 'failed';

        ws.send(JSON.stringify({
          type: 'test_complete',
          status: status,
          message: `Test ${status} with exit code ${code}`
        }));

        // Upload video and screenshot if test completed (regardless of pass/fail)
        if (authToken) {
          try {
            await this.uploadTestVideo(testData, authToken);
          } catch (videoError) {
            console.error('Failed to upload test video:', videoError);
          }

          try {
            await this.uploadTestScreenshot(testData, authToken);
          } catch (screenshotError) {
            console.error('Failed to upload test screenshot:', screenshotError);
          }
        } else {
          console.log('No auth token available for video/screenshot upload');
        }

        // No cleanup needed since we're using environment variables
      });

      testProcess.on('error', (error) => {
        console.error('Test process error:', error);
        ws.send(JSON.stringify({
          type: 'test_error',
          message: error.message
        }));
      });

    } catch (error: any) {
      console.error('Error executing test:', error);
      ws.send(JSON.stringify({
        type: 'test_error',
        message: error.message || 'Test execution failed'
      }));
    }
  }

  /**
   * Graceful shutdown
   */
  async close() {
    console.log('🔄 Shutting down TestRun WebSocket server...');
    await this.queueService.close();
    this.wss.close();
    console.log('✅ TestRun WebSocket server shut down');
  }

  // Upload test video to backend
  async uploadTestVideo(testData: TestExecutionRequest, authToken: string): Promise<void> {
    let tcId = 'unknown';
    try {
      const fs = require('fs');
      const path = require('path');
      const FormData = require('form-data');

      // Look for video files in test-results directory
      const testResultsDir = path.join(process.cwd(), 'test-results');

      if (!fs.existsSync(testResultsDir)) {
        console.log('No test-results directory found, skipping video upload');
        return;
      }

      // Find video files - look for patterns like {projectId}/{testRunId}/{tcId}/video.webm
      const projectId = testData.projectId || 'unknown-project';
      const testRunId = testData.testRunId || 'unknown-testrun';
      const tcId = testData.tcId || 'unknown-tc';

      // Define the specific video path for this test case only
      const expectedVideoPath = path.join(testResultsDir, projectId, testRunId, tcId.toString(), 'video.webm');
      console.log(`Looking for specific video file: ${expectedVideoPath}`);

      // Only look for the video file that belongs to this specific test case
      let videoFile: string | null = null;

      if (fs.existsSync(expectedVideoPath)) {
        const stats = fs.statSync(expectedVideoPath);
        if (stats.size > 0) {
          videoFile = expectedVideoPath;
          console.log(`Found expected video file: ${videoFile} (size: ${stats.size} bytes)`);
        } else {
          console.log(`Video file exists but is empty: ${expectedVideoPath}`);
        }
      }

      if (!videoFile) {
        // Fallback: look for video in testCaseId directory (if tcId and testCaseId are different)
        const fallbackVideoPath = path.join(testResultsDir, projectId, testRunId, testData.testCaseId, 'video.webm');
        if (fs.existsSync(fallbackVideoPath)) {
          const stats = fs.statSync(fallbackVideoPath);
          if (stats.size > 0) {
            videoFile = fallbackVideoPath;
            console.log(`Found fallback video file: ${videoFile} (size: ${stats.size} bytes)`);
          } else {
            console.log(`Fallback video file exists but is empty: ${fallbackVideoPath}`);
          }
        }
      }

      if (!videoFile) {
        console.log(`No valid video file found for test case ${tcId} (testCaseId: ${testData.testCaseId})`);
        console.log(`Expected paths:`);
        console.log(`  - ${expectedVideoPath}`);
        console.log(`  - ${path.join(testResultsDir, projectId, testRunId, testData.testCaseId, 'video.webm')}`);

        // List all files in the test results directory for debugging
        try {
          const testCaseDir = path.join(testResultsDir, projectId, testRunId, tcId.toString());
          if (fs.existsSync(testCaseDir)) {
            const files = fs.readdirSync(testCaseDir);
            console.log(`Files in ${testCaseDir}:`, files);
          } else {
            console.log(`Test case directory does not exist: ${testCaseDir}`);
          }
        } catch (dirError) {
          console.log(`Error reading test case directory:`, dirError);
        }
        return;
      }

      console.log(`Found video file for upload: ${videoFile}`);

      // Wait a moment for the database transaction to complete
      console.log('Waiting for database transaction to complete...');
      await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay (increased from 2)

      // Get the test result by testCaseId to find the result ID (with retry logic)
      let testResult: any = null;
      let retryCount = 0;
      const maxRetries = 3;

      while (!testResult && retryCount < maxRetries) {
        try {
          console.log(`Fetching test results (attempt ${retryCount + 1}/${maxRetries}) from: ${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results`);
          const testResultResponse = await axios.get(
            `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results?page=1&limit=100`,
            {
              headers: {
                'Authorization': `Bearer ${authToken}`
              },
              timeout: 10000 // Increased timeout
            }
          );
          console.log(`Test results response:`, testResultResponse.data);

          // Handle paginated response structure
          const testResults = testResultResponse.data.results || testResultResponse.data;

          if (!testResults || testResults.length === 0) {
            console.log(`No test results found (attempt ${retryCount + 1})`);
            retryCount++;
            if (retryCount < maxRetries) {
              console.log('Waiting 2 seconds before retry...');
              await new Promise(resolve => setTimeout(resolve, 2000));
            }
            continue;
          }

          // Get the most recent test result for this test case (where isLatest = true)
          testResult = testResults.find((result: any) =>
            result.testCaseId === testData.testCaseId && result.isLatest === true
          );

          if (!testResult) {
            console.log(`No matching latest test result found for testCaseId: ${testData.testCaseId} (attempt ${retryCount + 1})`);
            console.log('Available test results:', testResults.map((r: any) => ({
              id: r.id,
              testCaseId: r.testCaseId,
              isLatest: r.isLatest,
              status: r.status
            })));
            retryCount++;
            if (retryCount < maxRetries) {
              console.log('Waiting 2 seconds before retry...');
              await new Promise(resolve => setTimeout(resolve, 2000));
            }
          }
        } catch (fetchError) {
          console.error(`Error fetching test results (attempt ${retryCount + 1}):`, fetchError);
          retryCount++;
          if (retryCount < maxRetries) {
            console.log('Waiting 2 seconds before retry...');
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }
      }

      if (!testResult) {
        console.error(`Failed to find test result after ${maxRetries} attempts for testCaseId: ${testData.testCaseId}`);
        return;
      }

      const testResultId = testResult.id;
      console.log(`Uploading video for test result ID: ${testResultId}`);
      console.log(`Video file path: ${videoFile}`);
      console.log(`Test case validation: tcId=${tcId}, testCaseId=${testData.testCaseId}, testResultId=${testResultId}`);

      // Verify the video file belongs to this test case by checking the path
      if (videoFile && (!videoFile.includes(`/${tcId}/`) && !videoFile.includes(`/${testData.testCaseId}/`))) {
        console.error(`Video file path mismatch! Expected path to contain /${tcId}/ or /${testData.testCaseId}/, but got: ${videoFile}`);
        return;
      }

      // Read the video file
      const videoBuffer = fs.readFileSync(videoFile);

      // Create form data for video upload
      const formData = new FormData();
      formData.append('file', videoBuffer, {
        filename: 'video.webm',
        contentType: 'video/webm'
      });

      // Upload video to backend with retry logic
      let uploadSuccess = false;
      let uploadRetries = 0;
      const maxUploadRetries = 3;

      while (!uploadSuccess && uploadRetries < maxUploadRetries) {
        try {
          console.log(`Uploading video (attempt ${uploadRetries + 1}/${maxUploadRetries})...`);
          await axios.post(
            `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results/${testResultId}/video`,
            formData,
            {
              headers: {
                ...formData.getHeaders(),
                'Authorization': `Bearer ${authToken}`
              },
              timeout: 30000, // 30 second timeout for video upload
              maxContentLength: 100 * 1024 * 1024, // 100MB max
              maxBodyLength: 100 * 1024 * 1024
            }
          );
          uploadSuccess = true;
          console.log(`Video uploaded successfully for test result: ${testResultId}`);
        } catch (uploadError) {
          uploadRetries++;
          console.error(`Video upload attempt ${uploadRetries} failed:`, uploadError);
          if (uploadRetries < maxUploadRetries) {
            console.log(`Waiting 3 seconds before retry...`);
            await new Promise(resolve => setTimeout(resolve, 3000));
          }
        }
      }

      if (!uploadSuccess) {
        throw new Error(`Failed to upload video after ${maxUploadRetries} attempts`);
      }

      // Keep the video file - do not delete it to prevent race conditions
      // The file will be preserved for potential other uploads or manual cleanup
      console.log(`Video upload completed, preserving file: ${videoFile}`);
      console.log(`Note: Video files are preserved in test-results directory to prevent race conditions`);

    } catch (error) {
      console.error('Failed to upload test video:', error);

      // Log more detailed error information
      if (axios.isAxiosError(error) && error.response) {
        console.error('Video upload response status:', error.response.status);
        console.error('Video upload response data:', error.response.data);
        console.error('Video upload URL was:', `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results/${tcId}/video`);

        // If it's a 500 error on the test results fetch, it might be an endpoint issue
        if (error.config?.url?.includes('/test-results') && !error.config?.url?.includes('/video')) {
          console.error('❌ Failed to fetch test results - the backend endpoint might not exist');
          console.error('❌ This means the video upload endpoint for formal test results is not implemented in the backend');
        }
      } else if (axios.isAxiosError(error) && error.request) {
        console.error('No response received for video upload');
        console.error('Video upload URL was:', `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results/${tcId}/video`);
      } else {
        console.error('Video upload error:', (error as Error).message);
      }
    }
  }

  // Upload test screenshot to backend
  async uploadTestScreenshot(testData: TestExecutionRequest, authToken: string): Promise<void> {
    let tcId = 'unknown';
    try {
      const fs = require('fs');
      const path = require('path');
      const FormData = require('form-data');

      // Look for screenshot files in test-results directory
      const testResultsDir = path.join(process.cwd(), 'test-results');

      if (!fs.existsSync(testResultsDir)) {
        console.log('No test-results directory found, skipping screenshot upload');
        return;
      }

      // Find screenshot file based on the directory structure: {projectId}/{testRunId}/{tcId}/screenshot.png
      const projectId = testData.projectId;
      const testRunId = testData.testRunId;
      tcId = (testData as any).testCase?.tcId?.toString() || (testData as any).tcId?.toString() || 'unknown';

      const expectedScreenshotPath = path.join(testResultsDir, projectId, testRunId, tcId, 'screenshot.png');
      console.log(`Looking for specific screenshot file: ${expectedScreenshotPath}`);
      console.log(`Test case details: tcId=${tcId}, testCaseId=${testData.testCaseId}`);

      // If custom screenshot doesn't exist, look for Playwright's default screenshot
      if (!fs.existsSync(expectedScreenshotPath)) {
        console.log('Custom screenshot not found, looking for Playwright default screenshot...');

        // Look for Playwright's default screenshot pattern
        const playwrightScreenshotPattern = path.join(testResultsDir, 'testrun-detail-*', 'test-failed-*.png');
        const glob = require('glob');
        const playwrightScreenshots = glob.sync(playwrightScreenshotPattern);

        if (playwrightScreenshots.length > 0) {
          const latestScreenshot = playwrightScreenshots[playwrightScreenshots.length - 1];
          console.log('Found Playwright screenshot:', latestScreenshot);

          // Create directory structure for our expected path
          const screenshotDir = path.dirname(expectedScreenshotPath);
          if (!fs.existsSync(screenshotDir)) {
            fs.mkdirSync(screenshotDir, { recursive: true });
          }

          // Copy Playwright screenshot to our expected location
          fs.copyFileSync(latestScreenshot, expectedScreenshotPath);
          console.log('Copied Playwright screenshot to:', expectedScreenshotPath);
        } else {
          console.log('No Playwright screenshot found either, skipping screenshot upload');
          return;
        }
      }

      console.log('Found screenshot file, proceeding with upload');

      // Wait a moment for the database transaction to complete
      console.log('Waiting for database transaction to complete...');
      await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay

      // Get test results to find the correct test result ID
      const axios = require('axios');
      console.log(`Fetching test results from: ${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results`);
      const testResultResponse = await axios.get(
        `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results?page=1&limit=100`,
        {
          headers: {
            'Authorization': `Bearer ${authToken}`
          },
          timeout: 5000
        }
      );
      console.log(`Test results response:`, testResultResponse.data);

      // Handle paginated response structure
      const testResults = testResultResponse.data.results || testResultResponse.data;

      if (!testResults || testResults.length === 0) {
        console.log('No test result found for screenshot upload');
        return;
      }

      // Get the most recent test result for this test case (where isLatest = true)
      const testResult = testResults.find((result: any) =>
        result.testCaseId === testData.testCaseId && result.isLatest === true
      );
      if (!testResult) {
        console.log('No matching latest test result found for screenshot upload');
        console.log('Available test results:', testResults.map((r: any) => ({
          id: r.id,
          testCaseId: r.testCaseId,
          isLatest: r.isLatest,
          status: r.status
        })));
        return;
      }

      const testResultId = testResult.id;
      console.log(`Uploading screenshot for test result ID: ${testResultId}`);
      console.log(`Screenshot file path: ${expectedScreenshotPath}`);
      console.log(`Test result details:`, {
        id: testResult.id,
        testCaseId: testResult.testCaseId,
        status: testResult.status,
        isLatest: testResult.isLatest
      });

      // Verify the screenshot file belongs to this test case by checking the path
      if (!expectedScreenshotPath.includes(`/${tcId}/`) && !expectedScreenshotPath.includes(`/${testData.testCaseId}/`)) {
        console.error(`Screenshot file path mismatch! Expected path to contain /${tcId}/ or /${testData.testCaseId}/, but got: ${expectedScreenshotPath}`);
        return;
      }

      // Read the screenshot file
      const screenshotBuffer = fs.readFileSync(expectedScreenshotPath);

      // Create form data for screenshot upload
      const formData = new FormData();
      formData.append('file', screenshotBuffer, {
        filename: 'screenshot.png',
        contentType: 'image/png'
      });

      // Upload screenshot to backend
      await axios.post(
        `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results/${testResultId}/screenshot`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            'Authorization': `Bearer ${authToken}`
          },
          timeout: 30000, // 30 second timeout for screenshot upload
          maxContentLength: 100 * 1024 * 1024, // 100MB max
          maxBodyLength: 100 * 1024 * 1024
        }
      );

      console.log('Screenshot uploaded successfully');

    } catch (error: any) {
      if (axios.isAxiosError(error) && error.response) {
        console.error('Screenshot upload failed with status:', error.response.status);
        console.error('Screenshot upload error response:', error.response.data);
      } else if (axios.isAxiosError(error) && error.request) {
        console.error('No response received for screenshot upload');
        console.error('Screenshot upload URL was:', `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results/${tcId}/screenshot`);
      } else {
        console.error('Screenshot upload error:', (error as Error).message);
      }
    }
  }


}

// Start the server
const server = new TestRunnerWebSocketServer(3009);

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('Shutting down TestRun WebSocket server...');
  server.close();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('Shutting down TestRun WebSocket server...');
  server.close();
  process.exit(0);
});

// Export upload functions for use by queue service
export async function uploadTestVideo(testData: any, authToken: string): Promise<void> {
  const server = new TestRunnerWebSocketServer();
  return server.uploadTestVideo(testData, authToken);
}

export async function uploadTestScreenshot(testData: any, authToken: string): Promise<void> {
  const server = new TestRunnerWebSocketServer();
  return server.uploadTestScreenshot(testData, authToken);
}

export default TestRunnerWebSocketServer;
