import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Storage } from '@google-cloud/storage';
import { Config } from '../config';

@Injectable()
export class StorageService {
  private readonly logger = new Logger(StorageService.name);
  private storage: Storage;
  private bucketName: string;
  private uploadLocks = new Map<string, Promise<void>>();

  constructor(private configService: ConfigService) {
    this.bucketName = Config.GCP_BUCKET_NAME;
    
    // Initialize Google Cloud Storage
    this.storage = new Storage({
      projectId: Config.GCP_PROJECT_ID,
      credentials: {
        client_email: Config.GCP_CLIENT_EMAIL,
        private_key: Config.GCP_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
    });
  }

  /**
   * Execute a function with a lock to prevent race conditions
   * @param lockKey - Unique key for the lock
   * @param fn - Function to execute
   * @returns Promise<T> - Result of the function
   */
  private async withLock<T>(lockKey: string, fn: () => Promise<T>): Promise<T> {
    // Wait for any existing lock to complete
    while (this.uploadLocks.has(lockKey)) {
      await this.uploadLocks.get(lockKey);
    }

    // Create a new lock
    const lockPromise = (async () => {
      try {
        return await fn();
      } finally {
        // Remove the lock when done
        this.uploadLocks.delete(lockKey);
      }
    })();

    this.uploadLocks.set(lockKey, lockPromise.then(() => {}));
    return lockPromise;
  }

  /**
   * Upload logs to Google Cloud Storage
   * @param logsData - Object containing projectId, testCaseId, testRunId, and testResultId
   * @param logs - Array of log messages
   * @returns Promise<string> - The GCS URL
   */
  async uploadLogs(logsData: { projectId: string; testCaseId: string; testRunId: string; testResultId: string }, logs: string[]): Promise<string> {
    const lockKey = `logs-${logsData.projectId}-${logsData.testRunId}-${logsData.testCaseId}`;
    return this.withLock(lockKey, async () => {
      try {
        // Clean up old logs for this test case first
        await this.cleanupOldTestCaseArtifacts(logsData.projectId, logsData.testRunId, logsData.testCaseId, logsData.testResultId, 'logs');

        const fileName = `test-results/logs/${logsData.projectId}/${logsData.testRunId}/${logsData.testCaseId}/${logsData.testResultId}/logs.json`;
        const file = this.storage.bucket(this.bucketName).file(fileName);

        const logData = {
          timestamp: new Date().toISOString(),
          logs,
          metadata: {
            totalLogs: logs.length,
            uploadedAt: new Date().toISOString(),
          },
        };

        // Upload the logs as JSON
        await file.save(JSON.stringify(logData, null, 2), {
          metadata: {
            contentType: 'application/json',
          },
        });

        this.logger.log(`Logs uploaded successfully for test result: ${logsData.testResultId}`);
        return `gs://${this.bucketName}/${fileName}`;
      } catch (error) {
        this.logger.error(`Failed to upload logs for test result ${logsData.testResultId}:`, error);
        throw new Error(`Failed to upload logs: ${error}`);
      }
    });
  }

  /**
   * Download logs from Google Cloud Storage
   * @param logsUrl - The GCS URL
   * @returns Promise<string[]> - Array of log messages
   */
  async downloadLogs(logsUrl: string): Promise<string[]> {
    try {
      // Extract file path from GCS URL
      const filePath = logsUrl.replace(`gs://${this.bucketName}/`, '');
      const file = this.storage.bucket(this.bucketName).file(filePath);

      const [contents] = await file.download();
      const logData = JSON.parse(contents.toString());

      return logData.logs || [];
    } catch (error) {
      this.logger.error(`Failed to download logs from ${logsUrl}:`, error);
      throw new Error(`Failed to download logs: ${error}`);
    }
  }

  /**
   * Delete logs from Google Cloud Storage
   * @param logsUrl - The GCS URL
   * @returns Promise<void>
   */
  async deleteLogs(logsUrl: string): Promise<void> {
    try {
      // Extract file path from GCS URL
      const filePath = logsUrl.replace(`gs://${this.bucketName}/`, '');
      const file = this.storage.bucket(this.bucketName).file(filePath);

      await file.delete();
      this.logger.log(`Logs deleted successfully: ${logsUrl}`);
    } catch (error) {
      this.logger.error(`Failed to delete logs from ${logsUrl}:`, error);
      throw new Error(`Failed to delete logs: ${error}`);
    }
  }

  /**
   * Check if the bucket exists and is accessible
   * @returns Promise<boolean>
   */
  async checkBucketAccess(): Promise<boolean> {
    try {
      const bucket = this.storage.bucket(this.bucketName);
      const [exists] = await bucket.exists();

      if (!exists) {
        this.logger.warn(`Bucket ${this.bucketName} does not exist`);
        return false;
      }

      // Try to list files to check permissions
      await bucket.getFiles({ maxResults: 1 });
      this.logger.log(`Bucket ${this.bucketName} is accessible`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to access bucket ${this.bucketName}:`, error);
      return false;
    }
  }

  /**
   * Upload screenshot to Google Cloud Storage
   * @param projectId - The project ID
   * @param testRunId - The test run ID
   * @param testCaseId - The test case ID
   * @param testResultId - The test result ID
   * @param screenshotBuffer - The screenshot file buffer
   * @param contentType - The screenshot content type (e.g., 'image/png')
   * @returns Promise<string> - The GCS URL
   */
  async uploadScreenshot(projectId: string, testRunId: string, testCaseId: string, testResultId: string, screenshotBuffer: Buffer, contentType: string = 'image/png'): Promise<string> {
    const lockKey = `screenshot-${projectId}-${testRunId}-${testCaseId}`;
    return this.withLock(lockKey, async () => {
      try {
        // Clean up old screenshots for this test case first
        await this.cleanupOldTestCaseArtifacts(projectId, testRunId, testCaseId, testResultId, 'screenshots');

        const fileName = `test-results/screenshots/${projectId}/${testRunId}/${testCaseId}/${testResultId}/screenshot.png`;
        const file = this.storage.bucket(this.bucketName).file(fileName);

        // Upload the screenshot
        await file.save(screenshotBuffer, {
          metadata: {
            contentType: contentType,
          },
        });

        this.logger.log(`Screenshot uploaded successfully for test result: ${testResultId}`);
        return `gs://${this.bucketName}/${fileName}`;
      } catch (error) {
        this.logger.error(`Failed to upload screenshot for test result ${testResultId}:`, error);
        throw new Error(`Failed to upload screenshot: ${error}`);
      }
    });
  }

  /**
   * Upload video to Google Cloud Storage
   * @param projectId - The project ID
   * @param testRunId - The test run ID
   * @param testCaseId - The test case ID
   * @param testResultId - The test result ID
   * @param videoBuffer - The video file buffer
   * @param contentType - The video content type (e.g., 'video/webm')
   * @returns Promise<string> - The GCS URL
   */
  async uploadVideo(projectId: string, testRunId: string, testCaseId: string, testResultId: string, videoBuffer: Buffer, contentType: string = 'video/webm'): Promise<string> {
    const lockKey = `video-${projectId}-${testRunId}-${testCaseId}`;
    return this.withLock(lockKey, async () => {
      try {
        // Clean up old videos for this test case first
        await this.cleanupOldTestCaseArtifacts(projectId, testRunId, testCaseId, testResultId, 'videos');

        const fileName = `test-results/videos/${projectId}/${testRunId}/${testCaseId}/${testResultId}/video.webm`;
        const file = this.storage.bucket(this.bucketName).file(fileName);

        // Upload the video
        await file.save(videoBuffer, {
          metadata: {
            contentType: contentType,
          },
        });

        this.logger.log(`Video uploaded successfully for test result: ${testResultId}`);
        return `gs://${this.bucketName}/${fileName}`;
      } catch (error) {
        this.logger.error(`Failed to upload video for test result ${testResultId}:`, error);
        throw new Error(`Failed to upload video: ${error}`);
      }
    });
  }

  /**
   * Clean up old artifacts for a specific test case, keeping only the latest test result
   * @param projectId - The project ID
   * @param testRunId - The test run ID
   * @param testCaseId - The test case ID
   * @param currentTestResultId - The current test result ID to keep
   * @param artifactType - The type of artifact ('videos', 'screenshots', 'logs')
   * @returns Promise<void>
   */
  private async cleanupOldTestCaseArtifacts(
    projectId: string,
    testRunId: string,
    testCaseId: string,
    currentTestResultId: string,
    artifactType: 'videos' | 'screenshots' | 'logs'
  ): Promise<void> {
    try {
      const prefix = `test-results/${artifactType}/${projectId}/${testRunId}/${testCaseId}/`;
      this.logger.log(`Cleaning up old ${artifactType} for test case ${testCaseId} with prefix: ${prefix}`);

      // List all files with this prefix
      const [files] = await this.storage.bucket(this.bucketName).getFiles({
        prefix: prefix
      });

      if (files.length === 0) {
        this.logger.log(`No existing ${artifactType} found for test case ${testCaseId}`);
        return;
      }

      // Filter out files that belong to the current test result
      const filesToDelete = files.filter(file => {
        const filePath = file.name;
        // Check if the file path contains the current test result ID
        return !filePath.includes(`/${currentTestResultId}/`);
      });

      if (filesToDelete.length > 0) {
        this.logger.log(`Found ${filesToDelete.length} old ${artifactType} files to delete for test case ${testCaseId}:`);
        filesToDelete.forEach(file => this.logger.log(`  - ${file.name}`));

        // Delete old files
        await Promise.all(filesToDelete.map(file => file.delete()));

        this.logger.log(`Successfully deleted ${filesToDelete.length} old ${artifactType} files for test case ${testCaseId}`);
      } else {
        this.logger.log(`No old ${artifactType} files to delete for test case ${testCaseId} (keeping current: ${currentTestResultId})`);
      }
    } catch (error) {
      this.logger.error(`Error cleaning up old ${artifactType} for test case ${testCaseId}:`, error);
      // Don't throw error - continue with upload even if cleanup fails
    }
  }

  /**
   * Delete all test run data from Google Cloud Storage
   * @param projectId - The project ID
   * @param testRunId - The test run ID to delete
   * @returns Promise<void>
   */
  async deleteTestRunData(projectId: string, testRunId: string): Promise<void> {
    try {
      this.logger.log(`Starting cleanup of test run data for: ${testRunId}`);

      // Define the prefixes for all test run related files
      const prefixes = [
        `test-results/videos/${projectId}/${testRunId}/`,
        `test-results/screenshots/${projectId}/${testRunId}/`,
        `test-results/logs/${projectId}/${testRunId}/`
      ];

      let totalDeleted = 0;

      // Delete files for each prefix
      for (const prefix of prefixes) {
        try {
          this.logger.log(`Deleting files with prefix: ${prefix}`);

          // List all files with this prefix
          const [files] = await this.storage.bucket(this.bucketName).getFiles({
            prefix: prefix
          });

          if (files.length > 0) {
            this.logger.log(`Found ${files.length} files to delete with prefix: ${prefix}`);

            // Delete all files
            await Promise.all(files.map(file => file.delete()));
            totalDeleted += files.length;

            this.logger.log(`Successfully deleted ${files.length} files with prefix: ${prefix}`);
          } else {
            this.logger.log(`No files found with prefix: ${prefix}`);
          }
        } catch (error) {
          this.logger.error(`Error deleting files with prefix ${prefix}:`, error);
          // Continue with other prefixes even if one fails
        }
      }

      this.logger.log(`Test run cleanup completed. Total files deleted: ${totalDeleted}`);
    } catch (error) {
      this.logger.error(`Failed to cleanup test run data for ${testRunId}:`, error);
      throw new Error(`Failed to cleanup test run data: ${error}`);
    }
  }

  /**
   * Get a signed URL for a file in Google Cloud Storage
   * @param gcsUrl - The GCS URL (gs://bucket-name/path/to/file)
   * @returns Promise<string> - The signed URL for direct access
   */
  async getSignedUrl(gcsUrl: string): Promise<string> {
    try {
      // Extract file path from GCS URL
      const filePath = gcsUrl.replace(`gs://${this.bucketName}/`, '');
      const file = this.storage.bucket(this.bucketName).file(filePath);

      // Generate a signed URL that expires in 1 hour
      const [signedUrl] = await file.getSignedUrl({
        version: 'v4',
        action: 'read',
        expires: Date.now() + 60 * 60 * 1000, // 1 hour
      });

      return signedUrl;
    } catch (error) {
      this.logger.error(`Failed to generate signed URL for ${gcsUrl}:`, error);
      throw new Error(`Failed to generate signed URL: ${error}`);
    }
  }
}
