import express from 'express';
import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { Queue } from 'bullmq';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.DASHBOARD_PORT || 3012;

// Redis connection configuration
const redisConnection = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB || '0'),
};

console.log('🔧 Dashboard Redis connection config:', {
  host: redisConnection.host,
  port: redisConnection.port,
  hasPassword: !!redisConnection.password,
  db: redisConnection.db
});

// Create queue instance for dashboard
const testQueue = new Queue('test-execution', {
  connection: redisConnection,
});

// Set up Bull Board
const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath('/admin/queues');

const { addQueue, removeQueue, setQueues, replaceQueues } = createBullBoard({
  queues: [new BullMQAdapter(testQueue)],
  serverAdapter: serverAdapter,
});

app.use('/admin/queues', serverAdapter.getRouter());

// Add a simple status endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'BullMQ Dashboard Server',
    dashboard: `http://localhost:${PORT}/admin/queues`,
    status: 'running'
  });
});

// Add queue stats endpoint
app.get('/api/queue-stats', async (req, res) => {
  try {
    const waiting = await testQueue.getWaiting();
    const active = await testQueue.getActive();
    const completed = await testQueue.getCompleted();
    const failed = await testQueue.getFailed();

    const stats = {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      total: waiting.length + active.length,
      jobs: {
        waiting: waiting.map(job => ({
          id: job.id,
          name: job.name,
          data: job.data,
          timestamp: job.timestamp
        })),
        active: active.map(job => ({
          id: job.id,
          name: job.name,
          data: job.data,
          timestamp: job.timestamp,
          processedOn: job.processedOn
        })),
        failed: failed.slice(0, 10).map(job => ({
          id: job.id,
          name: job.name,
          data: job.data,
          failedReason: job.failedReason,
          timestamp: job.timestamp
        }))
      }
    };

    res.json(stats);
  } catch (error) {
    console.error('Error getting queue stats:', error);
    res.status(500).json({ error: 'Failed to get queue stats' });
  }
});

// Add endpoint to clear failed jobs
app.post('/api/clear-failed', async (req, res) => {
  try {
    await testQueue.clean(0, 1000, 'failed');
    res.json({ message: 'Failed jobs cleared' });
  } catch (error) {
    console.error('Error clearing failed jobs:', error);
    res.status(500).json({ error: 'Failed to clear failed jobs' });
  }
});

// Add endpoint to clear completed jobs
app.post('/api/clear-completed', async (req, res) => {
  try {
    await testQueue.clean(0, 1000, 'completed');
    res.json({ message: 'Completed jobs cleared' });
  } catch (error) {
    console.error('Error clearing completed jobs:', error);
    res.status(500).json({ error: 'Failed to clear completed jobs' });
  }
});

// Add endpoint to retry failed jobs
app.post('/api/retry-failed', async (req, res) => {
  try {
    const failedJobs = await testQueue.getFailed();
    let retriedCount = 0;
    
    for (const job of failedJobs) {
      await job.retry();
      retriedCount++;
    }
    
    res.json({ 
      message: `Retried ${retriedCount} failed jobs`,
      retriedCount 
    });
  } catch (error) {
    console.error('Error retrying failed jobs:', error);
    res.status(500).json({ error: 'Failed to retry failed jobs' });
  }
});

app.listen(PORT, () => {
  console.log(`🎛️  BullMQ Dashboard running on http://localhost:${PORT}`);
  console.log(`📊 Queue Dashboard: http://localhost:${PORT}/admin/queues`);
  console.log(`📈 Queue Stats API: http://localhost:${PORT}/api/queue-stats`);
  console.log(`🧹 Management APIs:`);
  console.log(`   - POST http://localhost:${PORT}/api/clear-failed`);
  console.log(`   - POST http://localhost:${PORT}/api/clear-completed`);
  console.log(`   - POST http://localhost:${PORT}/api/retry-failed`);
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('🔄 Shutting down dashboard server...');
  await testQueue.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('🔄 Shutting down dashboard server...');
  await testQueue.close();
  process.exit(0);
});
