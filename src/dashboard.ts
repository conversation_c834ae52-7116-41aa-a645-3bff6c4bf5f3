import express from 'express';
import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { Queue } from 'bullmq';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.DASHBOARD_PORT || 3012;

// Redis connection configuration
const redisConnection = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB || '0'),
};

console.log('🔧 Dashboard Redis connection config:', {
  host: redisConnection.host,
  port: redisConnection.port,
  hasPassword: !!redisConnection.password,
  db: redisConnection.db
});

// Create queue instance for dashboard
const testQueue = new Queue('test-execution', {
  connection: redisConnection,
});

// Set up Bull Board
const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath('/admin/queues');

const { addQueue } = createBullBoard({
  queues: [new BullMQAdapter(testQueue)],
  serverAdapter: serverAdapter,
});

app.use('/admin/queues', serverAdapter.getRouter());

// Add a simple status endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'BullMQ Dashboard Server',
    dashboard: `http://localhost:${PORT}/admin/queues`,
    status: 'running'
  });
});

app.listen(PORT, () => {
  console.log(`🎛️  BullMQ Dashboard running on http://localhost:${PORT}`);
  console.log(`📊 Queue Dashboard: http://localhost:${PORT}/admin/queues`);
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('🔄 Shutting down dashboard server...');
  await testQueue.close();
  process.exit(0);
});
