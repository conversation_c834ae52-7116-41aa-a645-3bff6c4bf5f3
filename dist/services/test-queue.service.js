"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestQueueService = void 0;
const bullmq_1 = require("bullmq");
const child_process_1 = require("child_process");
const path = __importStar(require("path"));
const ws_1 = __importDefault(require("ws"));
const axios_1 = __importDefault(require("axios"));
const dotenv = __importStar(require("dotenv"));
// Load environment variables
dotenv.config();
class TestQueueService {
    constructor() {
        this.activeConnections = new Map();
        // Redis connection configuration
        this.redisConnection = {
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT || '6379'),
            password: process.env.REDIS_PASSWORD || undefined,
            db: parseInt(process.env.REDIS_DB || '0'),
            maxRetriesPerRequest: 3,
            retryDelayOnFailover: 100,
            enableReadyCheck: false,
            maxLoadingTimeout: 1000,
        };
        console.log('🔧 Redis connection config:', {
            host: this.redisConnection.host,
            port: this.redisConnection.port,
            hasPassword: !!this.redisConnection.password,
            db: this.redisConnection.db
        });
        // Initialize queue with error handling
        try {
            this.queue = new bullmq_1.Queue('test-execution', {
                connection: this.redisConnection,
                defaultJobOptions: {
                    removeOnComplete: 10, // Keep last 10 completed jobs
                    removeOnFail: 50, // Keep last 50 failed jobs
                    attempts: 3, // Retry failed jobs up to 3 times
                    backoff: {
                        type: 'exponential',
                        delay: 2000,
                    },
                },
            });
            console.log('✅ Queue initialized successfully');
        }
        catch (queueError) {
            console.error('❌ Failed to initialize queue:', queueError);
            throw queueError;
        }
        // Initialize worker with error handling
        try {
            this.worker = new bullmq_1.Worker('test-execution', this.processTestJob.bind(this), {
                connection: this.redisConnection,
                concurrency: 1, // Process only 1 test at a time
            });
            console.log('✅ Worker initialized successfully');
            this.setupEventHandlers();
            // Test Redis connection
            this.testRedisConnection();
            console.log('🚀 Test Queue Service initialized');
        }
        catch (workerError) {
            console.error('❌ Failed to initialize worker:', workerError);
            throw workerError;
        }
    }
    /**
     * Test Redis connection
     */
    async testRedisConnection() {
        try {
            console.log('🔍 Testing Redis connection...');
            const stats = await this.getQueueStats();
            console.log('✅ Redis connection successful. Queue stats:', stats);
        }
        catch (error) {
            console.error('❌ Redis connection test failed:', error);
            if (error instanceof Error && error.message.includes('NOAUTH')) {
                console.error('💡 Redis authentication failed. Please check REDIS_PASSWORD in .env file');
                console.error('💡 Current password configured:', this.redisConnection.password ? '[HIDDEN]' : '[NONE]');
            }
        }
    }
    /**
     * Add a WebSocket connection to track
     */
    addConnection(wsId, ws) {
        this.activeConnections.set(wsId, ws);
        console.log(`📡 WebSocket connection added: ${wsId}`);
    }
    /**
     * Remove a WebSocket connection
     */
    removeConnection(wsId) {
        this.activeConnections.delete(wsId);
        console.log(`📡 WebSocket connection removed: ${wsId}`);
    }
    /**
     * Add a test job to the queue
     */
    async addTestJob(testData, authToken, wsId, backendUrl) {
        const jobData = {
            testData,
            authToken,
            wsId,
            backendUrl,
        };
        const job = await this.queue.add('execute-test', jobData, {
            jobId: `test-${testData.projectId}-${testData.testRunId}-${testData.testCaseId}-${Date.now()}`,
        });
        console.log(`📋 Test job added to queue: ${job.id}`);
        // Notify client about queue position
        this.sendToClient(wsId, {
            type: 'test_queued',
            jobId: job.id,
            message: 'Test added to execution queue',
        });
        return job;
    }
    /**
     * Get queue statistics
     */
    async getQueueStats() {
        const waiting = await this.queue.getWaiting();
        const active = await this.queue.getActive();
        const completed = await this.queue.getCompleted();
        const failed = await this.queue.getFailed();
        return {
            waiting: waiting.length,
            active: active.length,
            completed: completed.length,
            failed: failed.length,
            total: waiting.length + active.length,
        };
    }
    /**
     * Process a test job
     */
    async processTestJob(job) {
        const { testData, authToken, wsId, backendUrl } = job.data;
        console.log(`🔄 Processing test job: ${job.id}`);
        console.log(`📋 Test Case: ${testData.testCase?.title}`);
        // Notify client that test is starting
        this.sendToClient(wsId, {
            type: 'test_start',
            jobId: job.id,
            message: 'Test execution started',
        });
        try {
            const result = await this.executePlaywrightTest(testData, authToken, wsId, backendUrl, job);
            // Notify client of completion
            this.sendToClient(wsId, {
                type: 'test_complete',
                jobId: job.id,
                status: result.status,
                message: result.message,
            });
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            console.error(`❌ Test job failed: ${job.id}`, error);
            // Notify client of failure
            this.sendToClient(wsId, {
                type: 'test_error',
                jobId: job.id,
                message: `Test execution failed: ${errorMessage}`,
            });
            throw error;
        }
    }
    /**
     * Execute Playwright test
     */
    async executePlaywrightTest(testData, authToken, wsId, backendUrl, job) {
        const { projectId, testRunId, testCaseId } = testData;
        // Fetch AgentQ API key from backend using JWT token
        let agentqApiKey = '';
        try {
            const apiKeysResponse = await axios_1.default.get(`${backendUrl}/api-keys`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            });
            if (apiKeysResponse.data && apiKeysResponse.data.length > 0) {
                agentqApiKey = apiKeysResponse.data[0].apiKey;
                console.log(`🔑 Retrieved AgentQ API key for test execution: ${job.id}`);
            }
        }
        catch (error) {
            console.warn(`⚠️ Failed to fetch AgentQ API key for job ${job.id}, test may fail:`, error);
        }
        // Use the fetched AgentQ API key or fallback to environment
        if (!agentqApiKey) {
            agentqApiKey = process.env.AGENTQ_API_KEY || process.env.VITE_AGENTQ_API_KEY || '';
        }
        return new Promise((resolve, reject) => {
            // Create test data content in the same format as the original WebSocket server
            const testDataContent = {
                testCase: {
                    id: testCaseId,
                    tcId: testData.tcId?.toString() || testCaseId,
                    title: testData.testCase?.title || 'Unknown Test',
                    precondition: testData.testCase?.precondition || '',
                    expectation: testData.testCase?.expectation || ''
                },
                projectId,
                testRunId,
                steps: testData.steps || [],
                authToken: authToken || null
            };
            console.log(`📋 Test data prepared:`, {
                testCaseId: testDataContent.testCase.id,
                tcId: testDataContent.testCase.tcId,
                title: testDataContent.testCase.title,
                stepsCount: testDataContent.steps.length,
                hasAuthToken: !!testDataContent.authToken
            });
            // Create test file path
            const testFilePath = path.join(__dirname, '../../tests/testrun-detail.spec.ts');
            console.log(`🚀 Starting Playwright test for job: ${job.id}`);
            // Execute the test
            const testProcess = (0, child_process_1.spawn)('npx', [
                'playwright',
                'test',
                testFilePath,
                '--reporter=line'
            ], {
                cwd: path.join(__dirname, '../..'), // Run from the root directory
                stdio: ['pipe', 'pipe', 'pipe'],
                env: {
                    ...process.env,
                    TEST_DATA: JSON.stringify(testDataContent), // Pass test data as JSON string
                    BACKEND_URL: backendUrl,
                    PROJECT_ID: projectId,
                    TEST_RUN_ID: testRunId,
                    TEST_CASE_ID: testCaseId,
                    AUTH_TOKEN: authToken || '',
                    AGENTQ_TOKEN: agentqApiKey, // AgentQ library looks for this variable
                    AGENTQ_API_KEY: agentqApiKey, // Alternative variable name
                    AGENTQ_JWT_TOKEN: authToken // Pass JWT token for AgentQ library authentication
                }
            });
            let stdout = '';
            let stderr = '';
            // Capture output
            testProcess.stdout?.on('data', (data) => {
                const output = data.toString();
                stdout += output;
                console.log(`[${job.id}] ${output.trim()}`);
                // Send real-time output to client
                this.sendToClient(wsId, {
                    type: 'test_output',
                    jobId: job.id,
                    output: output.trim(),
                });
            });
            testProcess.stderr?.on('data', (data) => {
                const output = data.toString();
                stderr += output;
                console.error(`[${job.id}] ${output.trim()}`);
                // Send real-time error output to client
                this.sendToClient(wsId, {
                    type: 'test_output',
                    jobId: job.id,
                    output: output.trim(),
                    isError: true,
                });
            });
            // Handle test completion
            testProcess.on('close', async (code) => {
                console.log(`✅ Test process completed for job ${job.id} with exit code: ${code}`);
                const status = code === 0 ? 'passed' : 'failed';
                const result = {
                    status,
                    exitCode: code || 0,
                    message: `Test ${status} with exit code ${code}`,
                };
                // Upload video and screenshot after test completion
                if (authToken) {
                    try {
                        await this.uploadTestArtifacts(testData, authToken, backendUrl);
                    }
                    catch (uploadError) {
                        console.error(`❌ Failed to upload artifacts for job ${job.id}:`, uploadError);
                    }
                }
                resolve(result);
            });
            testProcess.on('error', (error) => {
                console.error(`❌ Test process error for job ${job.id}:`, error);
                reject(error);
            });
        });
    }
    /**
     * Upload test artifacts (video and screenshot)
     */
    async uploadTestArtifacts(testData, authToken, backendUrl) {
        // Import the upload functions from the original test runner
        const { uploadTestVideo, uploadTestScreenshot } = await Promise.resolve().then(() => __importStar(require('../test-runner-testrun')));
        try {
            await uploadTestVideo(testData, authToken);
            console.log(`📹 Video uploaded successfully for test: ${testData.testCaseId}`);
        }
        catch (videoError) {
            console.error('❌ Failed to upload test video:', videoError);
        }
        try {
            await uploadTestScreenshot(testData, authToken);
            console.log(`📸 Screenshot uploaded successfully for test: ${testData.testCaseId}`);
        }
        catch (screenshotError) {
            console.error('❌ Failed to upload test screenshot:', screenshotError);
        }
    }
    /**
     * Send message to WebSocket client
     */
    sendToClient(wsId, message) {
        const ws = this.activeConnections.get(wsId);
        if (ws && ws.readyState === ws_1.default.OPEN) {
            ws.send(JSON.stringify(message));
        }
    }
    /**
     * Setup event handlers for queue and worker
     */
    setupEventHandlers() {
        // Queue events
        this.queue.on('error', (error) => {
            console.error('❌ Queue error:', error);
        });
        // Worker events
        this.worker.on('completed', (job, result) => {
            console.log(`✅ Job completed: ${job.id} - ${result.status}`);
        });
        this.worker.on('failed', (job, error) => {
            console.error(`❌ Job failed: ${job?.id}`, error);
        });
        this.worker.on('progress', (job, progress) => {
            console.log(`📊 Job progress: ${job.id} - ${progress}%`);
            // Send progress to client
            if (job.data.wsId) {
                this.sendToClient(job.data.wsId, {
                    type: 'test_progress',
                    jobId: job.id,
                    progress,
                });
            }
        });
    }
    /**
     * Graceful shutdown
     */
    async close() {
        console.log('🔄 Shutting down Test Queue Service...');
        await this.worker.close();
        await this.queue.close();
        console.log('✅ Test Queue Service shut down');
    }
}
exports.TestQueueService = TestQueueService;
