"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const api_1 = require("@bull-board/api");
const bullMQAdapter_1 = require("@bull-board/api/bullMQAdapter");
const express_2 = require("@bull-board/express");
const bullmq_1 = require("bullmq");
const dotenv = __importStar(require("dotenv"));
// Load environment variables
dotenv.config();
const app = (0, express_1.default)();
const PORT = process.env.DASHBOARD_PORT || 3012;
// Redis connection configuration
const redisConnection = {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD || undefined,
    db: parseInt(process.env.REDIS_DB || '0'),
};
console.log('🔧 Dashboard Redis connection config:', {
    host: redisConnection.host,
    port: redisConnection.port,
    hasPassword: !!redisConnection.password,
    db: redisConnection.db
});
// Create queue instance for dashboard
const testQueue = new bullmq_1.Queue('test-execution', {
    connection: redisConnection,
});
// Set up Bull Board
const serverAdapter = new express_2.ExpressAdapter();
serverAdapter.setBasePath('/admin/queues');
const { addQueue } = (0, api_1.createBullBoard)({
    queues: [new bullMQAdapter_1.BullMQAdapter(testQueue)],
    serverAdapter: serverAdapter,
});
app.use('/admin/queues', serverAdapter.getRouter());
// Add a simple status endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'BullMQ Dashboard Server',
        dashboard: `http://localhost:${PORT}/admin/queues`,
        status: 'running'
    });
});
app.listen(PORT, () => {
    console.log(`🎛️  BullMQ Dashboard running on http://localhost:${PORT}`);
    console.log(`📊 Queue Dashboard: http://localhost:${PORT}/admin/queues`);
});
// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('🔄 Shutting down dashboard server...');
    await testQueue.close();
    process.exit(0);
});
