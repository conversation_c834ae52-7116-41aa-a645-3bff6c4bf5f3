"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PageContext = void 0;
class PageContext {
    constructor() {
        this.page = null;
    }
    static getInstance() {
        if (!PageContext.instance) {
            PageContext.instance = new PageContext();
        }
        return PageContext.instance;
    }
    setPage(page) {
        this.page = page;
    }
    getPage() {
        if (!this.page) {
            throw new Error('Page context not initialized. Make sure to call setPage() first.');
        }
        return this.page;
    }
    hasPage() {
        return this.page !== null;
    }
    clearPage() {
        this.page = null;
    }
}
exports.PageContext = PageContext;
