"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Config = void 0;
const dotenv_1 = require("dotenv");
(0, dotenv_1.config)();
exports.Config = {
    NODE_ENV: process.env.NODE_ENV || 'development',
    MIDTRANS_CLIENT_KEY: process.env.MIDTRANS_CLIENT_KEY,
    MIDTRANS_SERVER_KEY: process.env.MIDTRANS_SERVER_KEY,
    CLIENT_URL: process.env.CLIENT_URL,
    API_URL: process.env.API_URL,
    APPLY_TAX: process.env.APPLY_TAX,
    // Add Google Cloud Storage config
    GCP_PROJECT_ID: process.env.GCP_PROJECT_ID,
    GCP_CLIENT_EMAIL: process.env.GCP_CLIENT_EMAIL,
    GCP_PRIVATE_KEY: process.env.GCP_PRIVATE_KEY,
    GCP_BUCKET_NAME: process.env.GCP_BUCKET_NAME,
    CORE_SERVICE_URL: process.env.CORE_SERVICE_URL,
};
