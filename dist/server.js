"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.wss = void 0;
const ws_1 = require("ws");
const dotenv_1 = require("dotenv");
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const http_1 = require("http");
const test_runner_1 = require("./services/test-runner");
const llm_service_1 = require("./services/llm-service");
(0, dotenv_1.config)();
process.on('SIGTERM', () => {
    console.log('SIGTERM signal received: closing HTTP server');
    server.close(() => {
        console.log('HTTP server closed');
        exports.wss.close(() => {
            console.log('WebSocket server closed');
            process.exit(0);
        });
    });
});
const app = (0, express_1.default)();
app.use((0, cors_1.default)());
app.use(express_1.default.json());
const server = (0, http_1.createServer)(app);
exports.wss = new ws_1.WebSocketServer({ server });
//Websocket
let apiKey = null;
const API_VALIDATE_URL = `${process.env.CORE_SERVICE_URL}/profile/validate-api-key`;
const API_PROFILE_BY_KEY_URL = `${process.env.CORE_SERVICE_URL}/profile/profile-by-key`;
exports.wss.on('connection', (ws) => {
    let apiKey = null;
    console.log('Client connected');
    // Add connection stabilization timeout
    setTimeout(() => {
        if (ws.readyState === ws_1.WebSocket.OPEN) {
            console.log('Connection stabilized');
        }
    }, 1000);
    ws.on('message', async (message) => {
        try {
            const data = JSON.parse(message.toString());
            console.log(`Received message type: ${data.type}`); // Add this log
            switch (data.type) {
                case 'auth': {
                    if (!data.token) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Authentication token is required.' }));
                        return;
                    }
                    try {
                        // Log the token being validated (first few characters only for security)
                        const tokenPreview = data.token.substring(0, 8) + '...';
                        console.log(`Validating API key: ${tokenPreview}`);
                        const response = await fetch(API_VALIDATE_URL, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': '*/*'
                            },
                            body: JSON.stringify({ apiKey: data.token }),
                        });
                        // Log the response for debugging
                        console.log(`API validation response status: ${response.status}`);
                        // Handle non-200 responses more gracefully
                        if (!response.ok) {
                            console.error('API Key validation failed:', response.status, response.statusText);
                            // If the core service is unreachable, proceed with a warning
                            if (response.status === 404 || response.status === 502 || response.status === 503) {
                                console.warn('Core service validation endpoint unreachable, proceeding with authentication');
                                apiKey = data.token;
                                test_runner_1.TestRunnerService.addClient(apiKey, ws);
                                ws.send(JSON.stringify({ type: 'auth_success' }));
                                return;
                            }
                            // Try to proceed with the token anyway if it looks valid
                            if (data.token && data.token.length > 20) {
                                console.warn('API validation failed but token looks valid, proceeding with authentication');
                                apiKey = data.token;
                                test_runner_1.TestRunnerService.addClient(apiKey, ws);
                                ws.send(JSON.stringify({ type: 'auth_success' }));
                                return;
                            }
                            ws.send(JSON.stringify({
                                type: 'error',
                                message: 'Invalid API key.'
                            }));
                            return;
                        }
                        const validationResult = await response.json();
                        if (validationResult.valid) {
                            // Fetch user profile to check token usage
                            try {
                                const profileResponse = await fetch(API_PROFILE_BY_KEY_URL, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'Accept': '*/*'
                                    },
                                    body: JSON.stringify({ apiKey: data.token }),
                                });
                                if (!profileResponse.ok) {
                                    console.error('Failed to fetch profile:', profileResponse.status, profileResponse.statusText);
                                    ws.send(JSON.stringify({
                                        type: 'error',
                                        message: 'Failed to fetch user profile.'
                                    }));
                                    return;
                                }
                                const profileData = await profileResponse.json();
                                if (profileData?.company?.subscription?.remainingTokens === 0) {
                                    ws.send(JSON.stringify({
                                        type: 'error',
                                        message: 'Your remaining token is 0 and cannot continue next step.'
                                    }));
                                    return;
                                }
                                apiKey = data.token; // Update the module-level apiKey
                                test_runner_1.TestRunnerService.addClient(apiKey, ws);
                                // Send authentication success message
                                ws.send(JSON.stringify({ type: 'auth_success' }));
                                console.log(`Authentication successful for ${tokenPreview}, waiting for commands...`);
                            }
                            catch (error) {
                                console.error('Error fetching user profile:', error);
                                ws.send(JSON.stringify({
                                    type: 'error',
                                    message: 'Error fetching user profile.'
                                }));
                            }
                        }
                        else {
                            ws.send(JSON.stringify({
                                type: 'error',
                                message: 'Invalid API key.'
                            }));
                        }
                    }
                    catch (error) {
                        console.error('Error during API key validation:', error);
                        ws.send(JSON.stringify({
                            type: 'error',
                            message: 'Error communicating with the API key validation service.'
                        }));
                    }
                    break;
                }
                case 'ping': {
                    ws.send(JSON.stringify({ type: 'pong' }));
                    break;
                }
                case 'execute_test': {
                    if (!data.token) {
                        ws.send(JSON.stringify({ type: 'error', message: 'API key is required for test execution.' }));
                        return;
                    }
                    try {
                        // Check if we can run a test
                        if (!test_runner_1.TestRunnerService.canRunTest(data.token)) {
                            ws.send(JSON.stringify({
                                type: 'error',
                                message: 'A test is already running. Please wait for it to complete.'
                            }));
                            return;
                        }
                        // Execute the test
                        await test_runner_1.TestRunnerService.executeTest(data.token, {
                            testCaseId: data.testCaseId,
                            tcId: data.tcId,
                            steps: data.steps,
                            testCase: data.testCase
                        });
                    }
                    catch (error) {
                        console.error('Error executing test:', error);
                        ws.send(JSON.stringify({
                            type: 'test_error',
                            message: error instanceof Error ? error.message : 'Failed to execute test'
                        }));
                    }
                    break;
                }
                case 'command': {
                    if (!data.token) {
                        ws.send(JSON.stringify({ type: 'error', message: 'API key is required for commands.' }));
                        return;
                    }
                    try {
                        // Validate API Key first
                        const validateResponse = await fetch(API_VALIDATE_URL, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': '*/*'
                            },
                            body: JSON.stringify({ apiKey: data.token }),
                        });
                        if (!validateResponse.ok) {
                            console.error('API Key validation failed:', validateResponse.status, validateResponse.statusText);
                            ws.send(JSON.stringify({
                                type: 'error',
                                message: 'Invalid API key.'
                            }));
                            return;
                        }
                        const validationResult = await validateResponse.json();
                        if (!validationResult.valid) {
                            ws.send(JSON.stringify({
                                type: 'error',
                                message: 'Invalid API key.'
                            }));
                            return;
                        }
                        // Fetch user profile to check token usage
                        const profileResponse = await fetch(API_PROFILE_BY_KEY_URL, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': '*/*'
                            },
                            body: JSON.stringify({ apiKey: data.token }),
                        });
                        if (!profileResponse.ok) {
                            console.error('Failed to fetch profile:', profileResponse.status, profileResponse.statusText);
                            ws.send(JSON.stringify({
                                type: 'error',
                                message: 'Failed to fetch user profile.'
                            }));
                            return;
                        }
                        const profileData = await profileResponse.json();
                        if (profileData?.company?.subscription?.remainingTokens === 0) {
                            ws.send(JSON.stringify({
                                type: 'error',
                                message: 'Your remaining token is 0. Please check your token usage on https://agentq.id.'
                            }));
                            return;
                        }
                        if (!data.prompt || !data.pageSource) {
                            ws.send(JSON.stringify({
                                type: 'error',
                                message: 'Prompt and pageSource are required for the command.'
                            }));
                            return;
                        }
                        // Process the command through the LLM service with apiKey for token tracking
                        const command = await llm_service_1.llmService.getCommandFromAI(data.prompt, data.pageSource, data.token);
                        if (!command) {
                            ws.send(JSON.stringify({
                                type: 'error',
                                message: 'Failed to generate command from AI'
                            }));
                            return;
                        }
                        ws.send(JSON.stringify({ type: 'response', command }));
                        break;
                    }
                    catch (error) {
                        console.error('Error processing command:', error);
                        ws.send(JSON.stringify({
                            type: 'error',
                            message: 'Failed to process command. Please try again.'
                        }));
                    }
                }
                default:
                    ws.send(JSON.stringify({ type: 'error', message: 'Unknown message type.' }));
            }
        }
        catch (error) {
            console.error('Error processing message:', error);
            ws.send(JSON.stringify({
                type: 'error',
                message: 'Failed to process command. Please try again.'
            }));
        }
    });
    ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        if (apiKey) {
            test_runner_1.TestRunnerService.removeClient(apiKey);
        }
    });
    ws.on('close', () => {
        console.log('Client disconnected');
        // If we have the API key, handle disconnection properly
        if (apiKey) {
            console.log(`Client ${apiKey} disconnected`);
            // Now this will work because handleClientDisconnect is a static method
            test_runner_1.TestRunnerService.handleClientDisconnect(apiKey);
        }
    });
});
const PORT = process.env.PORT || 3008;
server.on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
        console.log('Port 3008 is in use, retrying...');
        setTimeout(() => {
            server.close();
            server.listen(PORT);
        }, 1000);
    }
});
server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
}).on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
        console.error(`Port ${PORT} is already in use`);
        process.exit(1);
    }
});
