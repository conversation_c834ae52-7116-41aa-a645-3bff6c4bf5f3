"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatTestOutput = formatTestOutput;
/**
 * Formats test output for better readability
 * @param output - Raw test output string
 * @returns Formatted output string
 */
function formatTestOutput(output) {
    if (!output)
        return '';
    // Strip ANSI color codes
    let formatted = output.replace(/[\u001b\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nqry=><]/g, '');
    // Remove progress indicators like [1A[2K
    formatted = formatted.replace(/\[\d+[A-Z]\[\d+[A-Z]/g, '');
    // Clean up extra whitespace
    formatted = formatted.trim();
    // Add emoji indicators for common messages
    if (formatted.includes('PASS') || formatted.includes('passed')) {
        formatted = '✅ ' + formatted;
    }
    else if (formatted.includes('FAIL') || formatted.includes('failed')) {
        formatted = '❌ ' + formatted;
    }
    else if (formatted.includes('Step:')) {
        formatted = '🔍 ' + formatted;
    }
    else if (formatted.includes('Error:') || formatted.includes('error:')) {
        formatted = '⚠️ ' + formatted;
    }
    else if (formatted.includes('Warning:') || formatted.includes('warning:')) {
        formatted = '⚠️ ' + formatted;
    }
    else if (formatted.includes('Running')) {
        formatted = '🏃 ' + formatted;
    }
    else if (formatted.includes('Loaded test data')) {
        formatted = '📂 ' + formatted;
    }
    // Format error messages for better readability
    if (formatted.includes('Timed out') && formatted.includes('expect(')) {
        try {
            // Extract the key information
            const locatorMatch = formatted.match(/Locator: locator\('([^']+)'\)/);
            const expectedMatch = formatted.match(/Expected string: "([^"]+)"/);
            const receivedMatch = formatted.match(/Received string: "([^"]+)"/);
            if (locatorMatch && expectedMatch && receivedMatch) {
                return `❌ Test failed: Element "${locatorMatch[1]}" has incorrect text.\n` +
                    `   Expected: "${expectedMatch[1]}"\n` +
                    `   Actual: "${receivedMatch[1]}"`;
            }
        }
        catch (e) {
            // If parsing fails, return the original formatted string
        }
    }
    return formatted;
}
